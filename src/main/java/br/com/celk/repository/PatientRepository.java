package br.com.celk.repository;

import br.com.celk.config.exception.RestException;
import br.com.celk.model.Patient;
import br.com.celk.model.TotalCount;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import io.quarkus.hibernate.orm.panache.PanacheRepository;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.Query;
import java.util.List;

@ApplicationScoped
public class PatientRepository extends BaseRepository implements PanacheRepository<Patient> {

    public List<Patient> listPatients(PatientParamV1DTO patientParamDTO) throws RestException {
        Query nativeQuery = this.entityManager.createNativeQuery(PatientRepositoryUtils.getListPatientsQuery(patientParamDTO), Patient.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        PatientRepositoryUtils.setListPatientsQueryParams(nativeQuery, patientParamDTO);
        PatientRepositoryUtils.setListPatientsQueryLimitOffsetParams(nativeQuery, patientParamDTO);
        //noinspection unchecked
        return nativeQuery.getResultList();
    }

    public Long countCareUnits(PatientParamV1DTO patientParamDTO) throws RestException {
        Query nativeQuery = this.entityManager.createNativeQuery(PatientRepositoryUtils.getCountPatientsQuery(patientParamDTO), TotalCount.class);
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        PatientRepositoryUtils.setListPatientsQueryParams(nativeQuery, patientParamDTO);
        return ((TotalCount) nativeQuery.getSingleResult()).getTotal() ;
    }

}
