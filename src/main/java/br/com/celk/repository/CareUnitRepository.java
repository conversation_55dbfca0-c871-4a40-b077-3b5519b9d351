package br.com.celk.repository;

import br.com.celk.model.CareUnit;
import br.com.celk.web.dto.params.PageParamDTO;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class CareUnitRepository extends BaseRepository implements PanacheRepository<CareUnit> {

    public List<CareUnit> listCareUnits(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);

        return findAll(Sort.by("careUnitId", Sort.Direction.Ascending))
                .page(Page.of(pageParamDTO.getQueryPageNumber(), dataSourceLimits.getListCareUnitLimit()))
                .list();
    }
}
