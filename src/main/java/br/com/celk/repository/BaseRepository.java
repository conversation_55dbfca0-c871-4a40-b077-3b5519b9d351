package br.com.celk.repository;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.config.tenant.RequestContext;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;

@ApplicationScoped
public abstract class BaseRepository {

    @Inject
    EntityManager entityManager;
    @Inject
    CelkDataSourceLimits dataSourceLimits;
    @Inject
    RequestContext requestContext;

    /**
     * Quando não existir uma conexão direta com banco, ex.: quando os endpoints se conectam com CS
     * o quarkus não executa a classe TenantResolver, dessa forma não é obtido o tenant da requisição.
     * Para resolver basta invocar esse método, onde ao executar EntityManager, o quarkus executa o resolver do tenant.
     */
    @Transactional
    public void startTenantResolver() {
        this.entityManager.clear();
    }

    public EntityManager getEntityManager() {
        return entityManager;
    }

    public void setEntityManager(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    public CelkDataSourceLimits getDataSourceLimits() {
        return dataSourceLimits;
    }

    public void setDataSourceLimits(CelkDataSourceLimits dataSourceLimits) {
        this.dataSourceLimits = dataSourceLimits;
    }

    public RequestContext getRequestContext() {
        return requestContext;
    }

    public void setRequestContext(RequestContext requestContext) {
        this.requestContext = requestContext;
    }
}
