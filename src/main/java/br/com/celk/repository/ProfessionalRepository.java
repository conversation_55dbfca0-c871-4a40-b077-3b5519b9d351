package br.com.celk.repository;

import br.com.celk.model.Professional;
import br.com.celk.web.dto.params.PageParamDTO;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class ProfessionalRepository extends BaseRepository implements PanacheRepository<Professional> {

    public List<Professional> listProfessionals(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        return findAll(Sort.by("professionalId", Sort.Direction.Ascending))
                .page(Page.of(pageParamDTO.getQueryPageNumber(), dataSourceLimits.getListCareUnitLimit()))
                .list();
    }
}
