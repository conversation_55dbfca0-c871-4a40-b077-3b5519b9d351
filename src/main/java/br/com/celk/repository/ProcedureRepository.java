package br.com.celk.repository;

import br.com.celk.model.Procedure;
import br.com.celk.web.dto.params.PageParamDTO;
import io.quarkus.hibernate.orm.panache.PanacheRepository;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;

import javax.enterprise.context.ApplicationScoped;
import java.util.List;

@ApplicationScoped
public class ProcedureRepository extends BaseRepository implements PanacheRepository<Procedure> {

    public List<Procedure> listProcedures(PageParamDTO pageParamDTO) {
        getRequestContext().setUsaConexaoDiretaComBanco(true);
        return findAll(Sort.by("procedureId", Sort.Direction.Ascending))
                .page(Page.of(pageParamDTO.getQueryPageNumber(), dataSourceLimits.getListCareUnitLimit()))
                .list();
    }
}
