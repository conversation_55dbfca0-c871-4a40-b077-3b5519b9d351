package br.com.celk.model;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "tipo_procedimento")
public class Procedure extends PanacheEntityBase implements Serializable {

    @Id
    @Column(name = "cd_tp_procedimento")
    private Long procedureId;

    @Column(name = "ds_tp_procedimento")
    private String procedureName;

    public Long getProcedureId() {
        return procedureId;
    }

    public void setProcedureId(Long procedureId) {
        this.procedureId = procedureId;
    }

    public String getProcedureName() {
        return procedureName;
    }

    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }

    @Override
    public String toString() {
        return "Procedure{" +
                "procedureId=" + procedureId +
                ", procedureName='" + procedureName + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Procedure procedure = (Procedure) o;
        return Objects.equals(procedureId, procedure.procedureId) &&
                Objects.equals(procedureName, procedure.procedureName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(procedureId, procedureName);
    }
}
