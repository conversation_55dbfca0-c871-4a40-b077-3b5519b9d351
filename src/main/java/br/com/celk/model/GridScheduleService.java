package br.com.celk.model;


import io.quarkus.hibernate.orm.panache.PanacheEntityBase;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "agenda_grade_atendimento")
public class GridScheduleService extends PanacheEntityBase implements Serializable {

    @Id
    @Column(name = "cd_ag_gra_atendimento")
    private Long gridScheduleServiceId;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "cd_ag_grade", referencedColumnName = "cd_ag_grade")
    private ScheduleGrid scheduleGrid;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "cd_tipo", referencedColumnName = "cd_tipo")
    private ScheduleGridType scheduleGridType;

    @Column(name = "tempo_medio")
    private Long averageProcedureTime;

    public Long getGridScheduleServiceId() {
        return gridScheduleServiceId;
    }

    public void setGridScheduleServiceId(Long gridScheduleServiceId) {
        this.gridScheduleServiceId = gridScheduleServiceId;
    }

    public ScheduleGrid getScheduleGrid() {
        return scheduleGrid;
    }

    public void setScheduleGrid(ScheduleGrid scheduleGrid) {
        this.scheduleGrid = scheduleGrid;
    }

    public ScheduleGridType getScheduleGridType() {
        return scheduleGridType;
    }

    public void setScheduleGridType(ScheduleGridType scheduleGridType) {
        this.scheduleGridType = scheduleGridType;
    }

    public Long getAverageProcedureTime() {
        return averageProcedureTime;
    }

    public void setAverageProcedureTime(Long averageProcedureTime) {
        this.averageProcedureTime = averageProcedureTime;
    }

    @Override
    public String toString() {
        return "GridScheduleService{" +
                "gridScheduleServiceId=" + gridScheduleServiceId +
                ", scheduleGrid=" + scheduleGrid +
                ", scheduleGridType=" + scheduleGridType +
                ", averageProcedureTime=" + averageProcedureTime +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GridScheduleService that = (GridScheduleService) o;
        return Objects.equals(gridScheduleServiceId, that.gridScheduleServiceId) &&
                Objects.equals(scheduleGrid, that.scheduleGrid) &&
                Objects.equals(scheduleGridType, that.scheduleGridType) &&
                Objects.equals(averageProcedureTime, that.averageProcedureTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(gridScheduleServiceId, scheduleGrid, scheduleGridType, averageProcedureTime);
    }
}


