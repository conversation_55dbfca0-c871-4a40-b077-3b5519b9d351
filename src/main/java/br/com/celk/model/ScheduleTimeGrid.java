package br.com.celk.model;

import io.quarkus.hibernate.orm.panache.PanacheEntityBase;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.Objects;

@Entity
@Table(name = "agenda_grade_horario")
public class ScheduleTimeGrid extends PanacheEntityBase implements Serializable {

    @Id
    @Column(name = "cd_agenda_horario")
    private Long scheduleTimeGridId;

    @Column(name = "hora")
    private OffsetDateTime time;

    @Column(name = "status")
    private Long status;

    @Column(name = "cd_ag_gra_atendimento")
    private Long serviceScheduleGridId;

    public Long getScheduleTimeGridId() {
        return scheduleTimeGridId;
    }

    public void setScheduleTimeGridId(Long scheduleTimeGridId) {
        this.scheduleTimeGridId = scheduleTimeGridId;
    }

    public OffsetDateTime getTime() {
        return time;
    }

    public void setTime(OffsetDateTime time) {
        this.time = time;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getServiceScheduleGridId() {
        return serviceScheduleGridId;
    }

    public void setServiceScheduleGridId(Long serviceScheduleGridId) {
        this.serviceScheduleGridId = serviceScheduleGridId;
    }

    @Override
    public String toString() {
        return "ScheduleTimeGrid{" +
                "scheduleTimeGridId=" + scheduleTimeGridId +
                ", time=" + time +
                ", status=" + status +
                ", serviceScheduleGridId=" + serviceScheduleGridId +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ScheduleTimeGrid that = (ScheduleTimeGrid) o;
        return Objects.equals(scheduleTimeGridId, that.scheduleTimeGridId) &&
                Objects.equals(time, that.time) &&
                Objects.equals(status, that.status) &&
                Objects.equals(serviceScheduleGridId, that.serviceScheduleGridId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(scheduleTimeGridId, time, status, serviceScheduleGridId);
    }
}
