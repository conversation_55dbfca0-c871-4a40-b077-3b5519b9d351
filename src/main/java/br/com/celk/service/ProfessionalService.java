package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.repository.ProfessionalRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.ProfessionalDTO;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.ProfessionalMapperImpl;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import java.util.List;

@ApplicationScoped
public class ProfessionalService {

    @Inject
    ProfessionalRepository professionalRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<ProfessionalDTO>> listProfessionals(@Valid PageParamDTO pageParamDTO) {

        return new ClkListResponseBuilder<List<ProfessionalDTO>>().builder()
                   .setData(new ProfessionalMapperImpl().toDto(this.professionalRepository.listProfessionals(pageParamDTO)))
                   .setPaging(PagingUtils.getPaging(pageParamDTO.getPageNumber(), dataSourceLimits.getListProfessionalsLimit()))
                   .build();
    }

    public ClkGenericData<Long> countProfessionals() {
        return new ClkGenericData<>(this.professionalRepository.count());
    }
}
