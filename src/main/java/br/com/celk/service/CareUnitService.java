package br.com.celk.service;

import br.com.celk.config.CelkDataSourceLimits;
import br.com.celk.repository.CareUnitRepository;
import br.com.celk.utils.PagingUtils;
import br.com.celk.web.builder.ClkListResponseBuilder;
import br.com.celk.web.dto.CareUnitDTO;
import br.com.celk.web.dto.ClkGenericData;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkPaginatedListResponse;
import br.com.celk.web.mapper.CareUnitMapperImpl;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import java.util.List;

@ApplicationScoped
public class CareUnitService {

    @Inject
    CareUnitRepository careUnitRepository;

    @Inject
    CelkDataSourceLimits dataSourceLimits;

    public ClkPaginatedListResponse<List<CareUnitDTO>> listCareUnits(@Valid PageParamDTO pageParamDTO) {
        return new ClkListResponseBuilder<List<CareUnitDTO>>().builder()
                   .setData(new CareUnitMapperImpl().toDto(this.careUnitRepository.listCareUnits(pageParamDTO)))
                   .setPaging(PagingUtils.getPaging(pageParamDTO.getPageNumber(), dataSourceLimits.getListCareUnitLimit()))
                   .build();
    }

    public ClkGenericData<Long> countCareUnits() {
        return new ClkGenericData<>(this.careUnitRepository.count());
    }
}
