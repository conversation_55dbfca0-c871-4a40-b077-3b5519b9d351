package br.com.celk.web.rest;

import br.com.celk.service.DynamoDynamoDbConfigService;
import br.com.celk.utils.ConstraintViolationUtils;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Utilitários", description = "Consultas auxiliares.")
@Path("/v1/api/municipios")
public class DynamoResource {

    @Inject
    DynamoDynamoDbConfigService dynamoDbConfigService;

    @GET
    @Path("/{uf}")
    @Timed
    @Operation(
            summary = "Retorna uma lista de municipios clientes da Celk por UF."
    )
    public Response getFilaAgendamentosLista(@PathParam("uf") @Valid String uf) {
        try {
            return Response.ok(dynamoDbConfigService.findByUf(uf.toUpperCase())).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }
}
