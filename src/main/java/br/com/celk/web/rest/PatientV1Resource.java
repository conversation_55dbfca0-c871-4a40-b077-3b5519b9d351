package br.com.celk.web.rest;

import br.com.celk.config.exception.RestException;
import br.com.celk.service.PatientService;
import br.com.celk.web.dto.PatientDTO;
import br.com.celk.web.dto.params.PatientParamV1DTO;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import java.util.List;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Deprecated")
@Path("/v1/api/patients")
public class PatientV1Resource {

    @Inject
    PatientService patientService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_patient")
    @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = PatientDTO[].class)))
    @Operation(
            summary = "Retorna informações de pacientes",
            description = "Retorna determinas informações do prontuário de pacientes.")
    public List<PatientDTO> listPacientes(@BeanParam @Valid PatientParamV1DTO patientParamDTO) throws RestException {
        return this.patientService.listPatients(patientParamDTO);
    }
}
