package br.com.celk.web.rest;

import br.com.celk.service.ProfessionalService;
import br.com.celk.utils.ConstraintViolationUtils;
import br.com.celk.web.dto.params.PageParamDTO;
import br.com.celk.web.dto.response.ClkErrorResponse;
import br.com.celk.web.dto.response.CountResponse;
import br.com.celk.web.dto.response.ProfessionalListResponse;
import org.eclipse.microprofile.metrics.annotation.Timed;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.media.Content;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponses;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import javax.annotation.security.RolesAllowed;
import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;
import javax.validation.ConstraintViolationException;
import javax.ws.rs.*;
import javax.ws.rs.core.Response;

@ApplicationScoped
@Produces("application/json")
@Consumes("application/json")
@Tag(name = "Profissionais")
@Path("/csaude/v1/professionals")
public class ProfessionalResource {

    @Inject
    ProfessionalService professionalService;

    @GET
    @Path("/")
    @Timed
    @RolesAllowed("read_professional")
    @APIResponses(value = {
        @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = ProfessionalListResponse.class))),
        @APIResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = ClkErrorResponse.class)))
    })
    @Operation(
            summary = "Retorna os profissionais",
            description = "Retorna determinadas informações dos profissionais da saúde pertencentes a um determinado município.")
    public Response listProfessionals(@BeanParam PageParamDTO pageParamDTO) {
        try {
            return Response.ok(professionalService.listProfessionals(pageParamDTO)).build();
        } catch (ConstraintViolationException e) {
            return ConstraintViolationUtils.treatConstraintViolation(e);
        }
    }

    @GET
    @Path("/count")
    @Timed
    @RolesAllowed("read_professional")
    @Operation(
            summary = "Retorna a quantidade total de profissionais",
            description = "Retorna a quantidade total de profissionais da saúde pertencentes a um determinado município.")
    @APIResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CountResponse.class)))
    public Response countProfessionals() {
        return Response.ok(professionalService.countProfessionals()).build();
    }
}
