package br.com.celk.config.tenant;

import javax.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class RequestContext {

    private static final ThreadLocal<String> requestMethod = new ThreadLocal<>();

    private boolean usaConexaoDiretaComBanco;

    public String getRequestMethod() {
        return requestMethod.get();
    }

    public void setRequestMethod(String method) {
        requestMethod.set(method);
    }

    public void clear() {
        requestMethod.remove();
    }

    public boolean isUsaConexaoDiretaComBanco() {
        return usaConexaoDiretaComBanco;
    }

    public void setUsaConexaoDiretaComBanco(boolean usaConexaoDiretaComBanco) {
        this.usaConexaoDiretaComBanco = usaConexaoDiretaComBanco;
    }
}