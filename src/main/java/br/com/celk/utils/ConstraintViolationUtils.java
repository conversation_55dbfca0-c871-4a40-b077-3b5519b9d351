package br.com.celk.utils;

import br.com.celk.web.dto.response.ClkError;
import br.com.celk.web.dto.response.ClkErrorResponse;

import javax.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

public class ConstraintViolationUtils {

    private ConstraintViolationUtils() {}

    public static Response treatConstraintViolation(ConstraintViolationException constraintViolationException) {
        List<ClkError> errors = new ArrayList<>();

        constraintViolationException.getConstraintViolations().forEach(violation -> {
            String[] fieldPath = violation.getPropertyPath().toString().split("\\.");

            errors.add(new ClkError().setField(fieldPath[fieldPath.length - 1])
                                     .setMessage(violation.getMessage()));
        });

        ClkErrorResponse clkResponse = new ClkErrorResponse().setErrors(errors)
                                                             .setMessage("Parameter Validation Failed");

        return Response.status(Response.Status.BAD_REQUEST.getStatusCode()).entity(clkResponse).build();
    }
}
