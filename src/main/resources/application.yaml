################## DEV CONFIGS ##################

'%dev':
  KEYCLOAK_USER_REALM: celk-realm
  KEYCLOAK_CLIENT_CREDENTIALS: ck_saude
  KEYCLOAK_CLIENT_CREDENTIALS_SECRET: fce8f8eb-71a1-4f2c-a38b-e6270b14e118

  DYNAMO_DB_TABLE_NAME: pda_database_data
  DYNAMO_DB_HOST: https://dynamodb.sa-east-1.amazonaws.com
  DYNAMO_DB_REGION: sa-east-1
  DYNAMO_DB_PORT: 443
  DYNAMO_DB_ACCESS_KEY: "********************"
  DYNAMO_DB_SECRET_KEY: "A+4GOq2v1hyNLVeItpx4PW+Zxjhhcj9Smk4bKkoo"

  DB_USER: pdaRole
  DB_PASSWORD: uy00cEdF5EazYyJa
  DB_HOST: terraform-20200616135042787500000001.cjzbxtd0xac2.sa-east-1.rds.amazonaws.com
  DB_PORT: 5432
  DB_NAME: pdaDB

  DB_POOL_MIN_SIZE: 1
  DB_POOL_MAX_SIZE: 10
  DB_MAX_LIFETIME: 60 #segundos
  DB_METRICS: true
  DB_INITIAL_SIZE: 1
  DB_ACQUISITION_TIMEOUT: 5 #segundos
  DB_LEAK_TIMEOUT: 5 #segundos
  DB_VALIDATION_TIMEOUT: 5 #segundos
  DB_IDLE_VALIDATION_TIMEOUT: 30 #segundos
  DB_REAP_TIMEOUT: 30 #segundos
  DB_AUTOCOMMIT: false
  DB_DRIVER: org.postgresql.Driver

  DB_LIMIT_PATIENTS: 50
  DB_LIMIT_CARE_UNITS: 20
  DB_LIMIT_PROCEDURES: 20
  DB_LIMIT_SCHEDULES: 20
  DB_LIMIT_PROFESSIONALS: 20
  DB_LIMIT_APPOINTMENTS: 500
  DB_LIMIT_MEDICAMENTOS: 500

  KEYCLOAK_URL: https://keycloak.apoio.celk.info/auth
  KEYCLOAK_REALM_PATH: /realms/api
  KEYCLOAK_OIDC_CLIENT_ID: developer-api
  KEYCLOAK_OIDC_CLIENT_SECRET: b3bf9a20-fa8a-4dd1-9b2c-57cfb2dc59a0

  LOG_LOGSTASH_HOST: logstash-logstash.elastic.svc.cluster.local
  LOG_GELF_PORT: 12201
  LOG_LEVEL: INFO
  LOG_ENVIRONMENT: apoio
  LOG_SERVICE_TYPE: microservice

  SAUDE_URL: "http://tenant:8080.celk.com.br"

  APP_PORT: 8081

  mp:
    openapi:
      extensions:
        smallrye:
          info:
            contact:
              email: <EMAIL>
              name: CELK API Support
              url: https://suporte.celk.com.br
            description: API para integração com parceiros.
            title: Celk API
            version: v1

  celk:
    datasource:
      config:
        metrics: ${DB_METRICS}
        min-size: ${DB_POOL_MIN_SIZE}
        max-size: ${DB_POOL_MAX_SIZE}
        max-lifetime: ${DB_MAX_LIFETIME}
        initial-size: ${DB_INITIAL_SIZE}
        acquisition-timeout: ${DB_ACQUISITION_TIMEOUT}
        leak-timeout: ${DB_LEAK_TIMEOUT}
        validation-timeout: ${DB_VALIDATION_TIMEOUT}
        idle-validation-timeout: ${DB_IDLE_VALIDATION_TIMEOUT}
        reap-timeout: ${DB_REAP_TIMEOUT}
        auto-commit: ${DB_AUTOCOMMIT}
        driver: ${DB_DRIVER}
      limits:
        list-patients-limit: ${DB_LIMIT_PATIENTS}
        list-care-unit-limit: ${DB_LIMIT_CARE_UNITS}
        list-procedures-limit: ${DB_LIMIT_PROCEDURES}
        list-schedules-limit: ${DB_LIMIT_SCHEDULES}
        list-professionals-limit: ${DB_LIMIT_PROFESSIONALS}
        list-appointments-limit: ${DB_LIMIT_APPOINTMENTS}
        list-medicamentos-limit: ${DB_LIMIT_MEDICAMENTOS}

    # Usado para que o PDA possa se comunicar com o Saúde
    keycloak:
      user-realm: ${KEYCLOAK_USER_REALM}
      client-credentials: ${KEYCLOAK_CLIENT_CREDENTIALS}
      client-credentials-secret: ${KEYCLOAK_CLIENT_CREDENTIALS_SECRET}

    dynamodb:
      table-name: ${DYNAMO_DB_TABLE_NAME}

    saude:
      url: ${SAUDE_URL}

  quarkus:
    ## All Hibernate settings must be entered in persistence.xml
    hibernate-orm:
      dialect: org.hibernate.dialect.PostgreSQLDialect
      log:
        sql: false
      multitenant: DATABASE

    ## Default Datasource ##
    dynamodb:
      endpoint-override: ${DYNAMO_DB_HOST}:${DYNAMO_DB_PORT}
      aws:
        region: ${DYNAMO_DB_REGION}
        credentials:
          type: static
          static-provider:
            access-key-id: ${DYNAMO_DB_ACCESS_KEY}
            secret-access-key: ${DYNAMO_DB_SECRET_KEY}

    datasource:
      db-kind: postgresql
      username: ${DB_USER}
      password: ${DB_PASSWORD}
      jdbc:
        url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
        enable-metrics: true

      master:
        db-kind: postgresql
        username: ${DB_USER}
        password: ${DB_PASSWORD}
        jdbc:
          url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
          enable-metrics: true

    # OIDC Configuration
    oidc:
      auth-server-url: ${KEYCLOAK_URL}${KEYCLOAK_REALM_PATH}
      client-id: ${KEYCLOAK_OIDC_CLIENT_ID}
      credentials:
        secret: ${KEYCLOAK_OIDC_CLIENT_SECRET}

    # Enable Policy Enforcement
    keycloak:
      policy-enforcer:
        enable: false
    http:
      port: ${APP_PORT}
      auth:
        permission:
          metrics:
            paths: "/metrics"
            policy: authenticated
          authenticated:
            paths: /csaude/*, /v1/api/municipios/*
            policy: authenticated
        proactive: true


    ## Log config ##
#    log:
#      level: INFO
#      handler:
#        gelf:
#          enabled: true
#          host: ${LOG_LOGSTASH_HOST}
#          port: ${LOG_GELF_PORT}
#          level: ${LOG_LEVEL}
#          stack-trace-throwable-reference: 1
#          include-full-mdc: true
#          filter-stack-trace: true
#          additional-field:
#            '"environment"':
#              value: ${LOG_ENVIRONMENT}
#            '"projectName"':
#              value: pda
#            '"serverType"':
#              value: ${LOG_SERVICE_TYPE}
#      console:
#        color: true
#        format: '%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n'
#        level: INFO

    ## Metrics ##
    smallrye-metrics:
      micrometer:
        compatibility: true

    ## OpenAPI Docs ##
    swagger-ui:
      always-include: true
      enable: true
      path: /docs

################## PROD CONFIGS ##################
mp:
  openapi:
    extensions:
      smallrye:
        info:
          contact:
            email: <EMAIL>
            name: CELK API Support
            url: https://suporte.celk.com.br
          description: API para integração com parceiros.
          title: Celk API
          version: v1

celk:
  datasource:
    config:
      metrics: ${DB_METRICS}
      min-size: ${DB_POOL_MIN_SIZE}
      max-size: ${DB_POOL_MAX_SIZE}
      max-lifetime: ${DB_MAX_LIFETIME}
      initial-size: ${DB_INITIAL_SIZE}
      acquisition-timeout: ${DB_ACQUISITION_TIMEOUT}
      leak-timeout: ${DB_LEAK_TIMEOUT}
      validation-timeout: ${DB_VALIDATION_TIMEOUT}
      idle-validation-timeout: ${DB_IDLE_VALIDATION_TIMEOUT}
      reap-timeout: ${DB_REAP_TIMEOUT}
      auto-commit: ${DB_AUTOCOMMIT}
      driver: ${DB_DRIVER}
    limits:
      list-patients-limit: ${DB_LIMIT_PATIENTS}
      list-care-unit-limit: ${DB_LIMIT_CARE_UNITS}
      list-procedures-limit: ${DB_LIMIT_PROCEDURES}
      list-schedules-limit: ${DB_LIMIT_SCHEDULES}
      list-professionals-limit: ${DB_LIMIT_PROFESSIONALS}
      list-appointments-limit: ${DB_LIMIT_APPOINTMENTS}
      list-medicamentos-limit: ${DB_LIMIT_MEDICAMENTOS}

  # Usado para que o PDA possa se comunicar com o Saúde
  keycloak:
    user-realm: ${KEYCLOAK_USER_REALM}
    client-credentials: ${KEYCLOAK_CLIENT_CREDENTIALS}
    client-credentials-secret: ${KEYCLOAK_CLIENT_CREDENTIALS_SECRET}

  dynamodb:
    table-name: ${DYNAMO_DB_TABLE_NAME}

  saude:
    url: ${SAUDE_URL}


quarkus:
  ## All Hibernate settings must be entered in persistence.xml
  hibernate-orm:
    dialect: org.hibernate.dialect.PostgreSQLDialect
    log:
      sql: false
    multitenant: DATABASE

  ## Default Datasource ##
  dynamodb:
    endpoint-override: ${DYNAMO_DB_HOST}:${DYNAMO_DB_PORT}
    aws:
      region: ${DYNAMO_DB_REGION}
      credentials:
        type: static
        static-provider:
          access-key-id: ${DYNAMO_DB_ACCESS_KEY}
          secret-access-key: ${DYNAMO_DB_SECRET_KEY}

  datasource:
    db-kind: postgresql
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    jdbc:
      url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
      enable-metrics: true

    master:
      db-kind: postgresql
      username: ${DB_USER}
      password: ${DB_PASSWORD}
      jdbc:
        url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}
        enable-metrics: true

  # OIDC Configuration
  oidc:
    auth-server-url: ${KEYCLOAK_URL}${KEYCLOAK_REALM_PATH}
    client-id: ${KEYCLOAK_OIDC_CLIENT_ID}
    credentials:
      secret: ${KEYCLOAK_OIDC_CLIENT_SECRET}

  # Enable Policy Enforcement
  keycloak:
    policy-enforcer:
      enable: false
  http:
    port: ${APP_PORT}
    auth:
      permission:
        metrics:
          paths: "/metrics"
          policy: authenticated
        authenticated:
          paths: /csaude/*, /v1/api/municipios/*
          policy: authenticated
      proactive: true


  ## Log config ##
  log:
    level: INFO
    handler:
      gelf:
        enabled: true
        host: ${LOG_LOGSTASH_HOST}
        port: ${LOG_GELF_PORT}
        level: ${LOG_LEVEL}
        stack-trace-throwable-reference: 1
        include-full-mdc: true
        filter-stack-trace: true
        additional-field:
          '"environment"':
            value: ${LOG_ENVIRONMENT}
          '"projectName"':
            value: pda
          '"serverType"':
            value: ${LOG_SERVICE_TYPE}
    console:
      color: true
      format: '%d{HH:mm:ss} %-5p [%c{2.}] (%t) %s%e%n'
      level: INFO

  ## Metrics ##
  smallrye-metrics:
    micrometer:
      compatibility: true

  ## OpenAPI Docs ##
  swagger-ui:
    always-include: true
    enable: true
    path: /docs
